directories:
  output: dist
  buildResources: build
appId: com.animamundi.espomienka-crm
productName: eSpomienka CRM
files:
  - filter:
      - '**/*'
      - '!node_modules'
      - '!dist'
      - '!.git'
      - '!README.md'
mac:
  category: public.app-category.business
  icon: assets/icon.icns
  identity: null
  target:
    - target: dmg
      arch:
        - x64
        - arm64
win:
  icon: assets/icon.ico
  target:
    - target: nsis
      arch:
        - x64
        - ia32
linux:
  icon: assets/icon.png
  category: Office
  target:
    - target: AppImage
      arch:
        - x64
    - target: deb
      arch:
        - x64
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: eSpomienka CRM
dmg:
  title: eSpomienka CRM
  backgroundColor: '#5e2e60'
electronVersion: 28.3.3
