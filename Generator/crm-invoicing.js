// CRM and Invoicing System
// Data structures and functionality for managing clients, invoices, and services

// Company data (predefined)
const COMPANY_DATA = {
    name: "Anima mundi spol. s r.o.",
    director: "<PERSON><PERSON><PERSON><PERSON>",
    address: "Zimná 316",
    city: "07602 Novosad",
    ico: "********",
    dic: "**********",
    icDph: "SK**********",
    bankAccount: "SK09 0900 0000 0051 4221 3602",
    phone: "+421 xxx xxx xxx", // configurable
    email: "<EMAIL>", // configurable
    website: "www.animamundi.sk" // configurable
};

// Service definitions with prices (as per prompt)
const SERVICES = {
    // Basic services
    "zakladna_male_urnove": { 
        name: "<PERSON><PERSON><PERSON><PERSON> údržba - Malé urnové miesto", 
        price: 12, 
        category: "basic" 
    },
    "zakladna_velke_urnove": { 
        name: "<PERSON><PERSON><PERSON><PERSON> údržba - Veľké urnové miesto", 
        price: 17, 
        category: "basic" 
    },
    "zakladna_jednohrob": { 
        name: "<PERSON><PERSON><PERSON><PERSON> údržba - Jednohrob", 
        price: 29, 
        category: "basic" 
    },
    "zakladna_dvojhrob": { 
        name: "Základná údržba - Dvojhrob", 
        price: 40, 
        category: "basic" 
    },
    
    // Deep cleaning
    "hlbkove_male_urnove": { 
        name: "Hĺbkové čistenie - Malé urnové miesto", 
        price: 45, 
        category: "basic" 
    },
    "hlbkove_velke_urnove": { 
        name: "Hĺbkové čistenie - Veľké urnové miesto", 
        price: 58, 
        category: "basic" 
    },
    "hlbkove_jednohrob": { 
        name: "Hĺbkové čistenie - Jednohrob", 
        price: 115, 
        category: "basic" 
    },
    "hlbkove_dvojhrob": { 
        name: "Hĺbkové čistenie - Dvojhrob", 
        price: 161, 
        category: "basic" 
    },
    
    // Holiday packages
    "sviatocny_male_urnove": { 
        name: "Balík Sviatočný - Malé urnové miesto", 
        price: 45, 
        period: "rok", 
        category: "package" 
    },
    "sviatocny_velke_urnove": { 
        name: "Balík Sviatočný - Veľké urnové miesto", 
        price: 63, 
        period: "rok", 
        category: "package" 
    },
    "sviatocny_jednohrob": { 
        name: "Balík Sviatočný - Jednohrob", 
        price: 104, 
        period: "rok", 
        category: "package" 
    },
    "sviatocny_dvojhrob": { 
        name: "Balík Sviatočný - Dvojhrob", 
        price: 150, 
        period: "rok", 
        category: "package" 
    },
    
    // Digital services
    "qr_zakladny": { 
        name: "QR kód - Základný balíček", 
        price: 35, 
        period: "rok", 
        category: "digital" 
    },
    "qr_rozsireny": { 
        name: "QR kód - Rozšírený balíček (3 roky)", 
        price: 65, 
        period: "3 roky", 
        category: "digital" 
    },
    "qr_rodinny": { 
        name: "QR kód - Rodinný balíček (5 rokov)", 
        price: 95, 
        period: "5 rokov", 
        category: "digital" 
    }
};

// Global data storage
let clients = [];
let invoices = [];
let invoiceCounter = 1;

// Data structure definitions
class Client {
    constructor(data) {
        this.id = data.id || generateId('CLI');
        this.name = data.name;
        this.phone = data.phone;
        this.email = data.email;
        this.address = data.address || '';
        this.ico = data.ico || '';
        this.dic = data.dic || '';
        this.graves = data.graves || [];
        this.notes = data.notes || '';
        this.totalRevenue = data.totalRevenue || 0;
        this.activeContracts = data.activeContracts || 0;
        this.createdAt = data.createdAt || new Date().toISOString();
    }
}

class Invoice {
    constructor(data) {
        this.id = data.id || generateId('INV');
        this.number = data.number || generateInvoiceNumber();
        this.clientId = data.clientId;
        this.issueDate = data.issueDate || new Date().toISOString().split('T')[0];
        this.dueDate = data.dueDate || calculateDueDate(2); // 2 days default
        this.items = data.items || [];
        this.subtotal = data.subtotal || 0;
        this.vat = data.vat || 0;
        this.total = data.total || 0;
        this.status = data.status || 'pending'; // pending/paid/overdue
        this.paidDate = data.paidDate || null;
        this.notes = data.notes || '';
        this.variableSymbol = data.variableSymbol || this.number;
        this.orderId = data.orderId || null; // link to order if created from order
    }
}

class InvoiceItem {
    constructor(data) {
        this.serviceId = data.serviceId;
        this.name = data.name;
        this.price = data.price;
        this.quantity = data.quantity || 1;
        this.total = data.total || (data.price * (data.quantity || 1));
    }
}

// Utility functions
function generateId(prefix) {
    return prefix + '-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
}

function generateInvoiceNumber() {
    const year = new Date().getFullYear();
    const number = String(invoiceCounter).padStart(3, '0');
    invoiceCounter++;
    return `${year}${number}`;
}

function calculateDueDate(days) {
    const date = new Date();
    date.setDate(date.getDate() + days);
    return date.toISOString().split('T')[0];
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('sk-SK', {
        style: 'currency',
        currency: 'EUR',
        minimumFractionDigits: 2
    }).format(amount);
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('sk-SK');
}

// VAT calculations (20% VAT rate for Slovakia)
function calculateVAT(amount) {
    return amount * 0.2;
}

function calculateSubtotal(total) {
    return total / 1.2;
}

// Data persistence
function saveClientsData() {
    localStorage.setItem('crm_clients', JSON.stringify(clients));
}

function loadClientsData() {
    const data = localStorage.getItem('crm_clients');
    if (data) {
        clients = JSON.parse(data).map(clientData => new Client(clientData));
    }
}

function saveInvoicesData() {
    localStorage.setItem('crm_invoices', JSON.stringify(invoices));
    localStorage.setItem('crm_invoice_counter', invoiceCounter.toString());
}

function loadInvoicesData() {
    const data = localStorage.getItem('crm_invoices');
    if (data) {
        invoices = JSON.parse(data).map(invoiceData => new Invoice(invoiceData));
    }
    
    const counter = localStorage.getItem('crm_invoice_counter');
    if (counter) {
        invoiceCounter = parseInt(counter);
    }
}

// Initialize CRM system
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('clients-tab')) {
        initializeCRMSystem();
    }
});

function initializeCRMSystem() {
    loadClientsData();
    loadInvoicesData();

    // Initialize tab navigation for new tabs
    const navTabs = document.querySelectorAll('.nav-tab');
    navTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const tabName = this.dataset.tab;
            switchTab(tabName);
        });
    });

    // Initialize search and filters
    initializeClientFilters();
    initializeInvoiceFilters();

    // Update displays
    updateClientsDisplay();
    updateInvoicesDisplay();
    updateServicesDisplay();
    updateCRMStats();
}

// Client management functions
function updateClientsDisplay() {
    const clientsList = document.getElementById('clientsList');
    if (!clientsList) return;

    if (clients.length === 0) {
        clientsList.innerHTML = `
            <div class="no-clients">
                <i class="fas fa-users"></i>
                <h4>Žiadni klienti</h4>
                <p>Začnite pridaním nového klienta</p>
                <button class="btn btn-primary" onclick="showAddClientModal()">
                    <i class="fas fa-plus"></i> Pridať prvého klienta
                </button>
            </div>
        `;
        return;
    }

    const searchTerm = document.getElementById('clientSearch')?.value.toLowerCase() || '';
    const sortBy = document.getElementById('clientSort')?.value || 'name';

    let filteredClients = clients.filter(client =>
        client.name.toLowerCase().includes(searchTerm) ||
        client.phone.includes(searchTerm) ||
        client.email.toLowerCase().includes(searchTerm)
    );

    // Sort clients
    filteredClients.sort((a, b) => {
        switch(sortBy) {
            case 'revenue':
                return b.totalRevenue - a.totalRevenue;
            case 'created':
                return new Date(b.createdAt) - new Date(a.createdAt);
            default: // name
                return a.name.localeCompare(b.name);
        }
    });

    clientsList.innerHTML = filteredClients.map(client => `
        <div class="client-item" data-client-id="${client.id}">
            <div class="client-header">
                <div class="client-avatar">${client.name.charAt(0).toUpperCase()}</div>
                <div class="client-info">
                    <h4 class="client-name">${client.name}</h4>
                    <div class="client-contact">
                        <span><i class="fas fa-phone"></i> ${client.phone}</span>
                        <span><i class="fas fa-envelope"></i> ${client.email}</span>
                    </div>
                    ${client.address ? `<div class="client-address"><i class="fas fa-map-marker-alt"></i> ${client.address}</div>` : ''}
                </div>
                <div class="client-stats">
                    <div class="stat">
                        <span class="stat-value">${formatCurrency(client.totalRevenue)}</span>
                        <span class="stat-label">Tržby</span>
                    </div>
                    <div class="stat">
                        <span class="stat-value">${client.activeContracts}</span>
                        <span class="stat-label">Zmluvy</span>
                    </div>
                </div>
            </div>
            <div class="client-actions">
                <button class="btn btn-secondary btn-sm" onclick="editClient('${client.id}')">
                    <i class="fas fa-edit"></i> Upraviť
                </button>
                <button class="btn btn-secondary btn-sm" onclick="viewClientInvoices('${client.id}')">
                    <i class="fas fa-file-invoice"></i> Faktúry
                </button>
                <button class="btn btn-primary btn-sm" onclick="createInvoiceForClient('${client.id}')">
                    <i class="fas fa-plus"></i> Nová faktúra
                </button>
                <button class="btn btn-danger btn-sm" onclick="deleteClient('${client.id}')">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `).join('');
}

function initializeClientFilters() {
    const searchInput = document.getElementById('clientSearch');
    const sortSelect = document.getElementById('clientSort');

    if (searchInput) {
        searchInput.addEventListener('input', updateClientsDisplay);
    }

    if (sortSelect) {
        sortSelect.addEventListener('change', updateClientsDisplay);
    }
}

function showAddClientModal() {
    // Create modal HTML if it doesn't exist
    let modal = document.getElementById('addClientModal');
    if (!modal) {
        modal = createClientModal();
        document.body.appendChild(modal);
    }

    // Reset form
    document.getElementById('addClientForm').reset();
    document.getElementById('clientModalTitle').textContent = 'Nový klient';
    document.getElementById('addClientForm').dataset.mode = 'add';

    modal.style.display = 'flex';
}

function createClientModal() {
    const modal = document.createElement('div');
    modal.id = 'addClientModal';
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="clientModalTitle"><i class="fas fa-user-plus"></i> Nový klient</h3>
                <span class="close" onclick="closeModal('addClientModal')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="addClientForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label>Meno a priezvisko *</label>
                            <input type="text" id="clientName" required>
                        </div>
                        <div class="form-group">
                            <label>Telefón *</label>
                            <input type="tel" id="clientPhone" required>
                        </div>
                        <div class="form-group">
                            <label>Email *</label>
                            <input type="email" id="clientEmail" required>
                        </div>
                        <div class="form-group">
                            <label>Adresa</label>
                            <input type="text" id="clientAddress">
                        </div>
                        <div class="form-group">
                            <label>IČO (pre firmy)</label>
                            <input type="text" id="clientIco">
                        </div>
                        <div class="form-group">
                            <label>DIČ (pre firmy)</label>
                            <input type="text" id="clientDic">
                        </div>
                        <div class="form-group full-width">
                            <label>Poznámky</label>
                            <textarea id="clientNotes" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('addClientModal')">
                            Zrušiť
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Uložiť
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;

    // Add form submit handler
    modal.querySelector('#addClientForm').addEventListener('submit', handleClientSubmit);

    return modal;
}

function handleClientSubmit(e) {
    e.preventDefault();

    const form = e.target;
    const mode = form.dataset.mode;
    const clientId = form.dataset.clientId;

    const clientData = {
        name: document.getElementById('clientName').value,
        phone: document.getElementById('clientPhone').value,
        email: document.getElementById('clientEmail').value,
        address: document.getElementById('clientAddress').value,
        ico: document.getElementById('clientIco').value,
        dic: document.getElementById('clientDic').value,
        notes: document.getElementById('clientNotes').value
    };

    if (mode === 'add') {
        const newClient = new Client(clientData);
        clients.push(newClient);
        showNotification('Klient bol úspešne pridaný', 'success');
    } else if (mode === 'edit') {
        const clientIndex = clients.findIndex(c => c.id === clientId);
        if (clientIndex !== -1) {
            Object.assign(clients[clientIndex], clientData);
            showNotification('Klient bol úspešne aktualizovaný', 'success');
        }
    }

    saveClientsData();
    updateClientsDisplay();
    updateCRMStats();
    closeModal('addClientModal');
}

function editClient(clientId) {
    const client = clients.find(c => c.id === clientId);
    if (!client) return;

    // Show modal
    showAddClientModal();

    // Fill form with client data
    document.getElementById('clientName').value = client.name;
    document.getElementById('clientPhone').value = client.phone;
    document.getElementById('clientEmail').value = client.email;
    document.getElementById('clientAddress').value = client.address || '';
    document.getElementById('clientIco').value = client.ico || '';
    document.getElementById('clientDic').value = client.dic || '';
    document.getElementById('clientNotes').value = client.notes || '';

    // Set form to edit mode
    document.getElementById('clientModalTitle').textContent = 'Upraviť klienta';
    document.getElementById('addClientForm').dataset.mode = 'edit';
    document.getElementById('addClientForm').dataset.clientId = clientId;
}

function deleteClient(clientId) {
    const client = clients.find(c => c.id === clientId);
    if (!client) return;

    if (confirm(`Naozaj chcete vymazať klienta "${client.name}"? Táto akcia sa nedá vrátiť späť.`)) {
        clients = clients.filter(c => c.id !== clientId);
        saveClientsData();
        updateClientsDisplay();
        updateCRMStats();
        showNotification('Klient bol vymazaný', 'success');
    }
}

function viewClientInvoices(clientId) {
    // Switch to invoices tab and filter by client
    switchTab('invoices');

    // Set client filter (we'll implement this in invoice functions)
    setTimeout(() => {
        const clientFilter = document.getElementById('invoiceClientFilter');
        if (clientFilter) {
            clientFilter.value = clientId;
            updateInvoicesDisplay();
        }
    }, 100);
}

function createInvoiceForClient(clientId) {
    // Switch to invoices tab and open new invoice modal with pre-filled client
    switchTab('invoices');

    setTimeout(() => {
        showAddInvoiceModal(clientId);
    }, 100);
}

function updateCRMStats() {
    // Update client stats
    const totalClients = clients.length;
    const totalRevenue = clients.reduce((sum, client) => sum + client.totalRevenue, 0);
    const activeContracts = clients.reduce((sum, client) => sum + client.activeContracts, 0);

    // Update invoice stats
    const totalInvoicesCount = invoices.length;
    const paidInvoicesCount = invoices.filter(inv => inv.status === 'paid').length;
    const pendingInvoicesCount = invoices.filter(inv => inv.status === 'pending').length;
    const totalInvoiceAmount = invoices.reduce((sum, inv) => sum + inv.total, 0);

    // Update DOM elements
    updateElementText('totalClients', totalClients);
    updateElementText('totalClientRevenue', formatCurrency(totalRevenue));
    updateElementText('activeClientContracts', activeContracts);

    updateElementText('totalInvoices', totalInvoicesCount);
    updateElementText('paidInvoices', paidInvoicesCount);
    updateElementText('pendingInvoicesCount', pendingInvoicesCount);
    updateElementText('totalInvoiceAmount', formatCurrency(totalInvoiceAmount));
}

function updateElementText(id, text) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = text;
    }
}

// Invoice management functions
function updateInvoicesDisplay() {
    const invoicesList = document.getElementById('invoicesList');
    if (!invoicesList) return;

    if (invoices.length === 0) {
        invoicesList.innerHTML = `
            <div class="no-invoices">
                <i class="fas fa-file-invoice"></i>
                <h4>Žiadne faktúry</h4>
                <p>Začnite vytvorením novej faktúry</p>
                <button class="btn btn-primary" onclick="showAddInvoiceModal()">
                    <i class="fas fa-plus"></i> Vytvoriť prvú faktúru
                </button>
            </div>
        `;
        return;
    }

    const statusFilter = document.getElementById('invoiceStatusFilter')?.value || 'all';
    const yearFilter = document.getElementById('invoiceYearFilter')?.value || 'all';
    const searchTerm = document.getElementById('invoiceSearch')?.value.toLowerCase() || '';

    let filteredInvoices = invoices.filter(invoice => {
        const matchesStatus = statusFilter === 'all' || invoice.status === statusFilter;
        const matchesYear = yearFilter === 'all' || new Date(invoice.issueDate).getFullYear().toString() === yearFilter;
        const matchesSearch = invoice.number.toLowerCase().includes(searchTerm) ||
                             getClientName(invoice.clientId).toLowerCase().includes(searchTerm);

        return matchesStatus && matchesYear && matchesSearch;
    });

    // Sort by invoice number (newest first)
    filteredInvoices.sort((a, b) => b.number.localeCompare(a.number));

    invoicesList.innerHTML = filteredInvoices.map(invoice => {
        const client = clients.find(c => c.id === invoice.clientId);
        const clientName = client ? client.name : 'Neznámy klient';
        const statusClass = getInvoiceStatusClass(invoice);
        const statusText = getInvoiceStatusText(invoice);

        return `
            <div class="invoice-item ${statusClass}" data-invoice-id="${invoice.id}">
                <div class="invoice-header">
                    <div class="invoice-number">
                        <i class="fas fa-file-invoice"></i>
                        <span class="number">${invoice.number}</span>
                    </div>
                    <div class="invoice-client">
                        <h4>${clientName}</h4>
                        <span class="invoice-date">${formatDate(invoice.issueDate)}</span>
                    </div>
                    <div class="invoice-amount">
                        <span class="amount">${formatCurrency(invoice.total)}</span>
                        <span class="status ${invoice.status}">${statusText}</span>
                    </div>
                </div>
                <div class="invoice-details">
                    <div class="detail-item">
                        <i class="fas fa-calendar"></i>
                        <span>Splatnosť: ${formatDate(invoice.dueDate)}</span>
                    </div>
                    ${invoice.paidDate ? `
                        <div class="detail-item">
                            <i class="fas fa-check"></i>
                            <span>Uhradené: ${formatDate(invoice.paidDate)}</span>
                        </div>
                    ` : ''}
                </div>
                <div class="invoice-actions">
                    <button class="btn btn-secondary btn-sm" onclick="viewInvoice('${invoice.id}')">
                        <i class="fas fa-eye"></i> Zobraziť
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="generateInvoicePDF('${invoice.id}')">
                        <i class="fas fa-file-pdf"></i> PDF
                    </button>
                    ${invoice.status === 'pending' ? `
                        <button class="btn btn-success btn-sm" onclick="markInvoiceAsPaid('${invoice.id}')">
                            <i class="fas fa-check"></i> Označiť ako uhradenú
                        </button>
                    ` : ''}
                    <button class="btn btn-primary btn-sm" onclick="editInvoice('${invoice.id}')">
                        <i class="fas fa-edit"></i> Upraviť
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="deleteInvoice('${invoice.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }).join('');
}

function getClientName(clientId) {
    const client = clients.find(c => c.id === clientId);
    return client ? client.name : 'Neznámy klient';
}

function getInvoiceStatusClass(invoice) {
    if (invoice.status === 'paid') return 'invoice-paid';
    if (invoice.status === 'overdue' || (invoice.status === 'pending' && new Date(invoice.dueDate) < new Date())) {
        return 'invoice-overdue';
    }
    return 'invoice-pending';
}

function getInvoiceStatusText(invoice) {
    switch(invoice.status) {
        case 'paid': return 'Uhradená';
        case 'overdue': return 'Po splatnosti';
        case 'pending':
            if (new Date(invoice.dueDate) < new Date()) {
                return 'Po splatnosti';
            }
            return 'Čaká na úhradu';
        default: return 'Neznámy stav';
    }
}

function initializeInvoiceFilters() {
    const statusFilter = document.getElementById('invoiceStatusFilter');
    const yearFilter = document.getElementById('invoiceYearFilter');
    const searchInput = document.getElementById('invoiceSearch');

    if (statusFilter) {
        statusFilter.addEventListener('change', updateInvoicesDisplay);
    }

    if (yearFilter) {
        yearFilter.addEventListener('change', updateInvoicesDisplay);
    }

    if (searchInput) {
        searchInput.addEventListener('input', updateInvoicesDisplay);
    }
}

function showAddInvoiceModal(preselectedClientId = null) {
    // Create modal HTML if it doesn't exist
    let modal = document.getElementById('addInvoiceModal');
    if (!modal) {
        modal = createInvoiceModal();
        document.body.appendChild(modal);
    }

    // Reset form
    document.getElementById('addInvoiceForm').reset();
    document.getElementById('invoiceModalTitle').textContent = 'Nová faktúra';
    document.getElementById('addInvoiceForm').dataset.mode = 'add';

    // Pre-select client if provided
    if (preselectedClientId) {
        const clientSelect = document.getElementById('invoiceClientSelect');
        if (clientSelect) {
            clientSelect.value = preselectedClientId;
        }
    }

    // Set default dates
    document.getElementById('invoiceIssueDate').value = new Date().toISOString().split('T')[0];
    document.getElementById('invoiceDueDate').value = calculateDueDate(2);

    // Generate invoice number
    document.getElementById('invoiceNumber').value = generateInvoiceNumber();

    modal.style.display = 'flex';

    // Populate client select
    populateClientSelect();
    updateInvoiceItemsDisplay();
}

function createInvoiceModal() {
    const modal = document.createElement('div');
    modal.id = 'addInvoiceModal';
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h3 id="invoiceModalTitle"><i class="fas fa-file-invoice-dollar"></i> Nová faktúra</h3>
                <span class="close" onclick="closeModal('addInvoiceModal')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="addInvoiceForm">
                    <div class="invoice-form-layout">
                        <div class="invoice-basic-info">
                            <h4>Základné údaje</h4>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label>Číslo faktúry</label>
                                    <input type="text" id="invoiceNumber" readonly>
                                </div>
                                <div class="form-group">
                                    <label>Dátum vystavenia</label>
                                    <input type="date" id="invoiceIssueDate" required>
                                </div>
                                <div class="form-group">
                                    <label>Dátum splatnosti</label>
                                    <input type="date" id="invoiceDueDate" required>
                                </div>
                                <div class="form-group">
                                    <label>Odberateľ *</label>
                                    <select id="invoiceClientSelect" required>
                                        <option value="">Vyberte klienta</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="invoice-items-section">
                            <h4>Položky faktúry</h4>
                            <div class="invoice-items-header">
                                <button type="button" class="btn btn-secondary btn-sm" onclick="addInvoiceItem()">
                                    <i class="fas fa-plus"></i> Pridať položku
                                </button>
                            </div>
                            <div class="invoice-items-list" id="invoiceItemsList">
                                <!-- Items will be populated by JavaScript -->
                            </div>
                        </div>

                        <div class="invoice-summary">
                            <h4>Súčet</h4>
                            <div class="summary-row">
                                <span>Základ bez DPH:</span>
                                <span id="invoiceSubtotal">0,00 EUR</span>
                            </div>
                            <div class="summary-row">
                                <span>DPH 20%:</span>
                                <span id="invoiceVAT">0,00 EUR</span>
                            </div>
                            <div class="summary-row total">
                                <span>Celkom s DPH:</span>
                                <span id="invoiceTotal">0,00 EUR</span>
                            </div>
                        </div>

                        <div class="invoice-notes">
                            <div class="form-group">
                                <label>Poznámka</label>
                                <textarea id="invoiceNotes" rows="3"></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="modal-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('addInvoiceModal')">
                            Zrušiť
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Uložiť faktúru
                        </button>
                        <button type="button" class="btn btn-success" onclick="saveAndGeneratePDF()">
                            <i class="fas fa-file-pdf"></i> Uložiť a generovať PDF
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;

    // Add form submit handler
    modal.querySelector('#addInvoiceForm').addEventListener('submit', handleInvoiceSubmit);

    return modal;
}

function populateClientSelect() {
    const clientSelect = document.getElementById('invoiceClientSelect');
    if (!clientSelect) return;

    clientSelect.innerHTML = '<option value="">Vyberte klienta</option>';

    clients.forEach(client => {
        const option = document.createElement('option');
        option.value = client.id;
        option.textContent = `${client.name} (${client.phone})`;
        clientSelect.appendChild(option);
    });
}

let invoiceItems = [];

function addInvoiceItem() {
    const newItem = {
        id: Date.now(),
        serviceId: '',
        name: '',
        price: 0,
        quantity: 1,
        total: 0
    };

    invoiceItems.push(newItem);
    updateInvoiceItemsDisplay();
}

function removeInvoiceItem(itemId) {
    invoiceItems = invoiceItems.filter(item => item.id !== itemId);
    updateInvoiceItemsDisplay();
}

function updateInvoiceItemsDisplay() {
    const itemsList = document.getElementById('invoiceItemsList');
    if (!itemsList) return;

    if (invoiceItems.length === 0) {
        itemsList.innerHTML = `
            <div class="no-items">
                <p>Žiadne položky. Kliknite na "Pridať položku" pre pridanie služby.</p>
            </div>
        `;
        updateInvoiceTotals();
        return;
    }

    itemsList.innerHTML = invoiceItems.map(item => `
        <div class="invoice-item-row" data-item-id="${item.id}">
            <select onchange="updateItemService(${item.id}, this.value)">
                <option value="">Vyberte službu</option>
                ${Object.entries(SERVICES).map(([key, service]) =>
                    `<option value="${key}" ${item.serviceId === key ? 'selected' : ''}>${service.name}</option>`
                ).join('')}
            </select>
            <input type="number" step="0.01" min="0" value="${item.price}"
                   onchange="updateItemPrice(${item.id}, this.value)" placeholder="Cena">
            <input type="number" min="1" value="${item.quantity}"
                   onchange="updateItemQuantity(${item.id}, this.value)" placeholder="Počet">
            <span class="item-total">${formatCurrency(item.total)}</span>
            <button type="button" class="remove-item" onclick="removeInvoiceItem(${item.id})">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `).join('');

    updateInvoiceTotals();
}

function updateItemService(itemId, serviceId) {
    const item = invoiceItems.find(i => i.id === itemId);
    if (!item) return;

    item.serviceId = serviceId;
    if (serviceId && SERVICES[serviceId]) {
        item.name = SERVICES[serviceId].name;
        item.price = SERVICES[serviceId].price;
    } else {
        item.name = '';
        item.price = 0;
    }

    item.total = item.price * item.quantity;
    updateInvoiceItemsDisplay();
}

function updateItemPrice(itemId, price) {
    const item = invoiceItems.find(i => i.id === itemId);
    if (!item) return;

    item.price = parseFloat(price) || 0;
    item.total = item.price * item.quantity;
    updateInvoiceItemsDisplay();
}

function updateItemQuantity(itemId, quantity) {
    const item = invoiceItems.find(i => i.id === itemId);
    if (!item) return;

    item.quantity = parseInt(quantity) || 1;
    item.total = item.price * item.quantity;
    updateInvoiceItemsDisplay();
}

function updateInvoiceTotals() {
    const subtotal = invoiceItems.reduce((sum, item) => sum + item.total, 0);
    const vat = calculateVAT(subtotal);
    const total = subtotal + vat;

    updateElementText('invoiceSubtotal', formatCurrency(subtotal));
    updateElementText('invoiceVAT', formatCurrency(vat));
    updateElementText('invoiceTotal', formatCurrency(total));
}

function handleInvoiceSubmit(e) {
    e.preventDefault();

    const form = e.target;
    const mode = form.dataset.mode;
    const invoiceId = form.dataset.invoiceId;

    const clientId = document.getElementById('invoiceClientSelect').value;
    if (!clientId) {
        alert('Prosím vyberte klienta');
        return;
    }

    if (invoiceItems.length === 0) {
        alert('Prosím pridajte aspoň jednu položku');
        return;
    }

    const subtotal = invoiceItems.reduce((sum, item) => sum + item.total, 0);
    const vat = calculateVAT(subtotal);
    const total = subtotal + vat;

    const invoiceData = {
        clientId: clientId,
        issueDate: document.getElementById('invoiceIssueDate').value,
        dueDate: document.getElementById('invoiceDueDate').value,
        items: invoiceItems.map(item => new InvoiceItem({
            serviceId: item.serviceId,
            name: item.name || SERVICES[item.serviceId]?.name || 'Vlastná služba',
            price: item.price,
            quantity: item.quantity,
            total: item.total
        })),
        subtotal: subtotal,
        vat: vat,
        total: total,
        notes: document.getElementById('invoiceNotes').value
    };

    if (mode === 'add') {
        const newInvoice = new Invoice(invoiceData);
        invoices.push(newInvoice);
        showNotification('Faktúra bola úspešne vytvorená', 'success');
    } else if (mode === 'edit') {
        const invoiceIndex = invoices.findIndex(inv => inv.id === invoiceId);
        if (invoiceIndex !== -1) {
            Object.assign(invoices[invoiceIndex], invoiceData);
            showNotification('Faktúra bola úspešne aktualizovaná', 'success');
        }
    }

    saveInvoicesData();
    updateInvoicesDisplay();
    updateCRMStats();
    closeModal('addInvoiceModal');

    // Reset invoice items
    invoiceItems = [];
}

function saveAndGeneratePDF() {
    // First save the invoice
    const form = document.getElementById('addInvoiceForm');
    const submitEvent = new Event('submit');
    form.dispatchEvent(submitEvent);

    // Then generate PDF for the last created invoice
    setTimeout(() => {
        if (invoices.length > 0) {
            const lastInvoice = invoices[invoices.length - 1];
            generateInvoicePDF(lastInvoice.id);
        }
    }, 100);
}

// Invoice actions
function viewInvoice(invoiceId) {
    const invoice = invoices.find(inv => inv.id === invoiceId);
    if (!invoice) return;

    // Create and show invoice detail modal
    showInvoiceDetailModal(invoice);
}

function editInvoice(invoiceId) {
    const invoice = invoices.find(inv => inv.id === invoiceId);
    if (!invoice) return;

    // Show modal
    showAddInvoiceModal();

    // Fill form with invoice data
    document.getElementById('invoiceNumber').value = invoice.number;
    document.getElementById('invoiceIssueDate').value = invoice.issueDate;
    document.getElementById('invoiceDueDate').value = invoice.dueDate;
    document.getElementById('invoiceClientSelect').value = invoice.clientId;
    document.getElementById('invoiceNotes').value = invoice.notes || '';

    // Set invoice items
    invoiceItems = invoice.items.map(item => ({
        id: Date.now() + Math.random(),
        serviceId: item.serviceId,
        name: item.name,
        price: item.price,
        quantity: item.quantity,
        total: item.total
    }));

    // Set form to edit mode
    document.getElementById('invoiceModalTitle').textContent = 'Upraviť faktúru';
    document.getElementById('addInvoiceForm').dataset.mode = 'edit';
    document.getElementById('addInvoiceForm').dataset.invoiceId = invoiceId;

    updateInvoiceItemsDisplay();
}

function deleteInvoice(invoiceId) {
    const invoice = invoices.find(inv => inv.id === invoiceId);
    if (!invoice) return;

    if (confirm(`Naozaj chcete vymazať faktúru ${invoice.number}? Táto akcia sa nedá vrátiť späť.`)) {
        invoices = invoices.filter(inv => inv.id !== invoiceId);
        saveInvoicesData();
        updateInvoicesDisplay();
        updateCRMStats();
        showNotification('Faktúra bola vymazaná', 'success');
    }
}

function markInvoiceAsPaid(invoiceId) {
    const invoice = invoices.find(inv => inv.id === invoiceId);
    if (!invoice) return;

    invoice.status = 'paid';
    invoice.paidDate = new Date().toISOString().split('T')[0];

    saveInvoicesData();
    updateInvoicesDisplay();
    updateCRMStats();
    showNotification(`Faktúra ${invoice.number} bola označená ako uhradená`, 'success');
}

function generateInvoicePDF(invoiceId) {
    const invoice = invoices.find(inv => inv.id === invoiceId);
    if (!invoice) return;

    const client = clients.find(c => c.id === invoice.clientId);
    if (!client) {
        alert('Klient pre túto faktúru nebol nájdený');
        return;
    }

    // Create PDF content
    const pdfContent = createInvoicePDFContent(invoice, client);

    // Generate PDF using html2pdf
    if (typeof window.html2pdf !== 'undefined') {
        const opt = {
            margin: 1,
            filename: `faktura_${invoice.number}.pdf`,
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: { scale: 2 },
            jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }
        };

        html2pdf().set(opt).from(pdfContent).save();
    } else {
        alert('PDF generátor nie je dostupný');
    }
}

function createInvoicePDFContent(invoice, client) {
    const today = new Date().toLocaleDateString('sk-SK');

    return `
        <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #5e2e60; margin-bottom: 10px;">FAKTÚRA</h1>
                <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                    <div style="text-align: left;">
                        <img src="https://storage.googleapis.com/espomienka/logo36.png" alt="Logo" style="height: 60px; margin-bottom: 10px;">
                        <div style="color: #5e2e60; font-weight: bold; font-size: 18px;">eSpomienka</div>
                    </div>
                    <div style="text-align: left;">
                        <strong>${COMPANY_DATA.name}</strong><br>
                        ${COMPANY_DATA.director}<br>
                        ${COMPANY_DATA.address}<br>
                        ${COMPANY_DATA.city}<br>
                        IČO: ${COMPANY_DATA.ico}<br>
                        DIČ: ${COMPANY_DATA.dic}<br>
                        IČ DPH: ${COMPANY_DATA.icDph}
                    </div>
                </div>
            </div>

            <div style="margin-bottom: 30px;">
                <div style="display: flex; justify-content: space-between;">
                    <div>
                        <strong>Číslo faktúry:</strong> ${invoice.number}<br>
                        <strong>Dátum vystavenia:</strong> ${formatDate(invoice.issueDate)}<br>
                        <strong>Dátum splatnosti:</strong> ${formatDate(invoice.dueDate)}<br>
                        <strong>Forma úhrady:</strong> Bezhotovostne
                    </div>
                </div>
            </div>

            <div style="margin-bottom: 30px; border: 1px solid #ddd; padding: 15px;">
                <h3 style="color: #5e2e60; margin-bottom: 10px;">ODBERATEĽ:</h3>
                <strong>${client.name}</strong><br>
                ${client.address || ''}<br>
                ${client.phone}<br>
                ${client.email}
                ${client.ico ? `<br>IČO: ${client.ico}` : ''}
                ${client.dic ? `<br>DIČ: ${client.dic}` : ''}
            </div>

            <div style="margin-bottom: 30px;">
                <h3 style="color: #5e2e60; margin-bottom: 15px;">DODANÉ SLUŽBY:</h3>
                <table style="width: 100%; border-collapse: collapse; border: 1px solid #ddd;">
                    <thead>
                        <tr style="background-color: #f8f9fa;">
                            <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">Názov služby</th>
                            <th style="border: 1px solid #ddd; padding: 10px; text-align: center;">Cena</th>
                            <th style="border: 1px solid #ddd; padding: 10px; text-align: center;">MJ</th>
                            <th style="border: 1px solid #ddd; padding: 10px; text-align: right;">Celkom</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${invoice.items.map(item => `
                            <tr>
                                <td style="border: 1px solid #ddd; padding: 10px;">${item.name}</td>
                                <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">${formatCurrency(item.price)}</td>
                                <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">${item.quantity} ks</td>
                                <td style="border: 1px solid #ddd; padding: 10px; text-align: right;">${formatCurrency(item.total)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>

            <div style="margin-bottom: 30px; text-align: right;">
                <div style="display: inline-block; text-align: left;">
                    <div style="margin-bottom: 5px;">
                        <span style="display: inline-block; width: 150px;">Základ bez DPH:</span>
                        <strong>${formatCurrency(invoice.subtotal)}</strong>
                    </div>
                    <div style="margin-bottom: 5px;">
                        <span style="display: inline-block; width: 150px;">DPH 20%:</span>
                        <strong>${formatCurrency(invoice.vat)}</strong>
                    </div>
                    <div style="border-top: 2px solid #5e2e60; padding-top: 5px; font-size: 18px;">
                        <span style="display: inline-block; width: 150px;">CELKOM K ÚHRADE:</span>
                        <strong style="color: #5e2e60;">${formatCurrency(invoice.total)}</strong>
                    </div>
                </div>
            </div>

            <div style="margin-bottom: 30px; border: 1px solid #ddd; padding: 15px;">
                <strong>Číslo účtu:</strong> ${COMPANY_DATA.bankAccount}<br>
                <strong>Variabilný symbol:</strong> ${invoice.variableSymbol}
            </div>

            <div style="text-align: center; margin-top: 40px;">
                <div style="margin-bottom: 10px;">
                    <strong>Faktúru vystavil:</strong> ${COMPANY_DATA.director}
                </div>
                <div style="margin-bottom: 10px;">
                    <strong>Dátum:</strong> ${today}
                </div>
                <div style="font-style: italic; color: #666;">
                    [Elektronická faktúra bez podpisu]
                </div>
            </div>

            ${invoice.notes ? `
                <div style="margin-top: 30px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
                    <strong>Poznámka:</strong><br>
                    ${invoice.notes}
                </div>
            ` : ''}
        </div>
    `;
}

// Services management functions
function updateServicesDisplay() {
    updateBasicServicesGrid();
    updatePackageServicesGrid();
    updateDigitalServicesGrid();
}

function updateBasicServicesGrid() {
    const grid = document.getElementById('basicServicesGrid');
    if (!grid) return;

    const basicServices = Object.entries(SERVICES).filter(([key, service]) => service.category === 'basic');

    grid.innerHTML = basicServices.map(([key, service]) => `
        <div class="service-edit-item">
            <div class="service-name">${service.name}</div>
            <div class="service-price-input">
                <input type="number" step="0.01" min="0" value="${service.price}"
                       onchange="updateServicePrice('${key}', this.value)">
                <span>EUR</span>
            </div>
        </div>
    `).join('');
}

function updatePackageServicesGrid() {
    const grid = document.getElementById('packageServicesGrid');
    if (!grid) return;

    const packageServices = Object.entries(SERVICES).filter(([key, service]) => service.category === 'package');

    grid.innerHTML = packageServices.map(([key, service]) => `
        <div class="service-edit-item">
            <div class="service-name">${service.name}</div>
            <div class="service-price-input">
                <input type="number" step="0.01" min="0" value="${service.price}"
                       onchange="updateServicePrice('${key}', this.value)">
                <span>EUR/${service.period || 'rok'}</span>
            </div>
        </div>
    `).join('');
}

function updateDigitalServicesGrid() {
    const grid = document.getElementById('digitalServicesGrid');
    if (!grid) return;

    const digitalServices = Object.entries(SERVICES).filter(([key, service]) => service.category === 'digital');

    grid.innerHTML = digitalServices.map(([key, service]) => `
        <div class="service-edit-item">
            <div class="service-name">${service.name}</div>
            <div class="service-price-input">
                <input type="number" step="0.01" min="0" value="${service.price}"
                       onchange="updateServicePrice('${key}', this.value)">
                <span>EUR/${service.period || 'rok'}</span>
            </div>
        </div>
    `).join('');
}

function updateServicePrice(serviceKey, newPrice) {
    if (SERVICES[serviceKey]) {
        SERVICES[serviceKey].price = parseFloat(newPrice) || 0;
        localStorage.setItem('crm_services', JSON.stringify(SERVICES));
        showNotification('Cena služby bola aktualizovaná', 'success');
    }
}

function resetServicePrices() {
    if (confirm('Naozaj chcete obnoviť všetky ceny služieb na predvolené hodnoty?')) {
        localStorage.removeItem('crm_services');
        location.reload(); // Reload to get default prices
    }
}

// Utility functions
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Show notification
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateY(0)';
    }, 100);

    // Hide and remove notification
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateY(-100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
    }
}

// Integration with existing order system
function createInvoiceFromOrder(orderId) {
    // This function will be called from the orders system
    // to create an invoice from a completed order

    // Find the order (assuming orders array exists from orders.js)
    if (typeof orders !== 'undefined') {
        const order = orders.find(o => o.id === orderId);
        if (!order) return;

        // Find or create client
        let client = clients.find(c =>
            c.name === order.customerName &&
            c.phone === order.customerPhone
        );

        if (!client) {
            // Create new client from order data
            client = new Client({
                name: order.customerName,
                phone: order.customerPhone,
                email: order.customerEmail || '',
                address: order.customerAddress || ''
            });
            clients.push(client);
            saveClientsData();
        }

        // Create invoice
        const invoiceData = {
            clientId: client.id,
            orderId: orderId,
            items: [{
                serviceId: order.serviceId || 'custom',
                name: order.serviceName || order.packageName,
                price: order.price || order.totalPrice,
                quantity: 1,
                total: order.price || order.totalPrice
            }]
        };

        const subtotal = invoiceData.items[0].total;
        invoiceData.subtotal = calculateSubtotal(subtotal);
        invoiceData.vat = calculateVAT(invoiceData.subtotal);
        invoiceData.total = subtotal;

        const newInvoice = new Invoice(invoiceData);
        invoices.push(newInvoice);
        saveInvoicesData();

        // Mark order as invoiced
        order.invoiced = true;
        order.invoiceId = newInvoice.id;

        showNotification(`Faktúra ${newInvoice.number} bola vytvorená z objednávky`, 'success');

        return newInvoice.id;
    }
}
